# ========================================
# DIV教育学习平台 - 统一配置文件
# ========================================

# 数据库类型配置 - 只需修改此参数即可切换数据库
# 支持的值: h2 | mysql
database:
  type: mysql  # 默认使用MySQL数据库，改为h2即可切换到H2内存数据库

server:
  port: 8081
  servlet:
    context-path: /api
  tomcat:
    basedir: ./tmp
    additional-tld-skip-patterns: "*.jar"

spring:
  application:
    name: div-education-platform

  # H2数据库配置
  datasource:
    h2:
      url: jdbc:h2:mem:testdb
      driver-class-name: org.h2.Driver
      username: sa
      password:
    # MySQL数据库配置
    mysql:
      url: *******************************************************************************************************************************************************
      username: root
      password: root
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000

  # JPA配置
  jpa:
    h2:
      hibernate:
        ddl-auto: create-drop
      show-sql: true
      properties:
        hibernate:
          dialect: org.hibernate.dialect.H2Dialect
          format_sql: true
    mysql:
      hibernate:
        ddl-auto: update
      show-sql: false
      properties:
        hibernate:
          dialect: org.hibernate.dialect.MySQL8Dialect
          format_sql: true

  h2:
    console:
      enabled: true
    
  security:
    user:
      name: admin
      password: admin123
      
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      fail-on-empty-beans: false
    properties:
      hibernate5-module:
        force-lazy-loading: false
        use-transient-annotation: false

# JWT配置
jwt:
  secret: divEducationPlatformSecretKey2024
  expiration: 86400000 # 24小时

# 日志配置
logging:
  level:
    com.div.education: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
