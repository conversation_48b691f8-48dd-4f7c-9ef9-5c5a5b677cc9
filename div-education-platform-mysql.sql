-- DIV教育学习平台 - 完整MySQL数据库脚本
-- 包含7张核心表和完整示例数据
-- MySQL 8.0+ 兼容

-- 创建数据库
CREATE DATABASE IF NOT EXISTS div_education 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE div_education;

-- ========================================
-- 1. 用户表
-- ========================================
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    role VARCHAR(20) NOT NULL DEFAULT 'STUDENT' COMMENT '角色：ADMIN/TEACHER/STUDENT',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE/INACTIVE/BANNED',
    bio VARCHAR(500) COMMENT '个人简介',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ========================================
-- 2. 课程表
-- ========================================
CREATE TABLE IF NOT EXISTS courses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '课程标题',
    description TEXT COMMENT '课程描述',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    instructor VARCHAR(100) NOT NULL COMMENT '讲师姓名',
    category VARCHAR(50) NOT NULL COMMENT '课程分类',
    level VARCHAR(20) NOT NULL DEFAULT 'BEGINNER' COMMENT '难度等级：BEGINNER/INTERMEDIATE/ADVANCED',
    duration INTEGER NOT NULL DEFAULT 0 COMMENT '课程时长（分钟）',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '课程价格',
    original_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '原价',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分（0-5）',
    student_count INTEGER NOT NULL DEFAULT 0 COMMENT '学生数量',
    lesson_count INTEGER NOT NULL DEFAULT 0 COMMENT '课时数量',
    status VARCHAR(20) NOT NULL DEFAULT 'PUBLISHED' COMMENT '状态：DRAFT/PUBLISHED/ARCHIVED',
    tags VARCHAR(500) COMMENT '标签（逗号分隔）',
    teacher_id BIGINT NOT NULL COMMENT '教师ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_teacher (teacher_id),
    INDEX idx_rating (rating),
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程表';

-- ========================================
-- 3. 实验表
-- ========================================
CREATE TABLE IF NOT EXISTS experiments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '实验标题',
    description TEXT COMMENT '实验描述',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    subject VARCHAR(50) NOT NULL COMMENT '学科',
    difficulty VARCHAR(20) NOT NULL DEFAULT 'BEGINNER' COMMENT '难度：BEGINNER/INTERMEDIATE/ADVANCED',
    duration INTEGER NOT NULL DEFAULT 0 COMMENT '实验时长（分钟）',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分（0-5）',
    completed_count INTEGER NOT NULL DEFAULT 0 COMMENT '完成人数',
    objectives TEXT COMMENT '实验目标（逗号分隔）',
    equipment TEXT COMMENT '实验器材（逗号分隔）',
    steps TEXT COMMENT '实验步骤（逗号分隔）',
    tags TEXT COMMENT '标签（逗号分隔）',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE/INACTIVE/MAINTENANCE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_subject (subject),
    INDEX idx_difficulty (difficulty),
    INDEX idx_status (status),
    INDEX idx_rating (rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实验表';

-- ========================================
-- 4. 帖子表
-- ========================================
CREATE TABLE IF NOT EXISTS posts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '帖子标题',
    content TEXT NOT NULL COMMENT '帖子内容',
    category VARCHAR(50) NOT NULL COMMENT '分类',
    tags TEXT COMMENT '标签（逗号分隔）',
    view_count INTEGER NOT NULL DEFAULT 0 COMMENT '浏览数',
    like_count INTEGER NOT NULL DEFAULT 0 COMMENT '点赞数',
    comment_count INTEGER NOT NULL DEFAULT 0 COMMENT '评论数',
    is_pinned BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否置顶',
    status VARCHAR(20) NOT NULL DEFAULT 'PUBLISHED' COMMENT '状态：DRAFT/PUBLISHED/HIDDEN/DELETED',
    author_id BIGINT NOT NULL COMMENT '作者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_author (author_id),
    INDEX idx_pinned (is_pinned),
    INDEX idx_created (created_at),
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子表';

-- ========================================
-- 5. 学习小组表
-- ========================================
CREATE TABLE IF NOT EXISTS study_groups (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '小组名称',
    description TEXT COMMENT '小组描述',
    cover_image VARCHAR(500) COMMENT '小组封面',
    category VARCHAR(50) COMMENT '分类',
    max_members INTEGER DEFAULT 100 COMMENT '最大成员数',
    current_members INTEGER DEFAULT 0 COMMENT '当前成员数',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    tags TEXT COMMENT '标签（逗号分隔）',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE/INACTIVE/ARCHIVED',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_creator (creator_id),
    INDEX idx_public (is_public),
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习小组表';

-- ========================================
-- 6. 新闻表
-- ========================================
CREATE TABLE IF NOT EXISTS news (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '新闻标题',
    summary TEXT COMMENT '新闻摘要',
    content TEXT COMMENT '新闻内容',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    category VARCHAR(50) COMMENT '分类',
    tags TEXT COMMENT '标签（逗号分隔）',
    source VARCHAR(100) COMMENT '来源',
    author VARCHAR(100) COMMENT '作者',
    view_count INTEGER DEFAULT 0 COMMENT '浏览数',
    like_count INTEGER DEFAULT 0 COMMENT '点赞数',
    is_hot BOOLEAN DEFAULT FALSE COMMENT '是否热门',
    status VARCHAR(20) NOT NULL DEFAULT 'PUBLISHED' COMMENT '状态：DRAFT/PUBLISHED/ARCHIVED',
    published_at TIMESTAMP NULL COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_hot (is_hot),
    INDEX idx_published (published_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻表';

-- ========================================
-- 7. 学习记录表
-- ========================================
CREATE TABLE IF NOT EXISTS learning_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    course_id BIGINT NOT NULL COMMENT '课程ID',
    progress INTEGER DEFAULT 0 COMMENT '学习进度（百分比）',
    last_position INTEGER DEFAULT 0 COMMENT '最后学习位置',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_user_course (user_id, course_id),
    INDEX idx_user (user_id),
    INDEX idx_course (course_id),
    INDEX idx_progress (progress),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习记录表';

-- ========================================
-- 插入示例数据
-- ========================================

-- 插入用户数据（密码：123456，已BCrypt加密）
INSERT INTO users (username, email, password, nickname, role, status, bio) VALUES
-- 管理员
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '系统管理员', 'ADMIN', 'ACTIVE', '系统管理员账号'),

-- 教师用户
('teacher1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '李老师', 'TEACHER', 'ACTIVE', '前端开发讲师，拥有10年开发经验，专注Vue.js和React技术栈'),
('teacher2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '王老师', 'TEACHER', 'ACTIVE', '后端开发专家，Java技术栈资深讲师，Spring Boot微服务架构师'),
('teacher3', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '陈老师', 'TEACHER', 'ACTIVE', 'Python数据科学讲师，机器学习和深度学习专家'),
('teacher4', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '刘老师', 'TEACHER', 'ACTIVE', '移动开发专家，Android和iOS开发资深讲师'),
('teacher5', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '张老师', 'TEACHER', 'ACTIVE', '数据库专家，MySQL和Redis性能优化专家'),
('teacher6', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '赵老师', 'TEACHER', 'ACTIVE', '云计算架构师，AWS和阿里云认证专家'),

-- 学生用户
('student1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '张同学', 'STUDENT', 'ACTIVE', '计算机科学专业大三学生，热爱前端开发'),
('student2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '李同学', 'STUDENT', 'ACTIVE', '软件工程专业大二学生，专注后端技术'),
('student3', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '王同学', 'STUDENT', 'ACTIVE', '数据科学专业研一学生，Python和机器学习爱好者'),
('student4', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '陈同学', 'STUDENT', 'ACTIVE', '信息管理专业大四学生，准备转行做程序员'),
('student5', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '刘同学', 'STUDENT', 'ACTIVE', '电子商务专业学生，对全栈开发感兴趣'),
('student6', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '赵同学', 'STUDENT', 'ACTIVE', '计算机网络专业学生，云计算和DevOps方向'),
('student7', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '孙同学', 'STUDENT', 'ACTIVE', '人工智能专业学生，深度学习研究方向'),
('student8', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '周同学', 'STUDENT', 'ACTIVE', '移动应用开发专业学生，Android开发爱好者'),
('student9', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '吴同学', 'STUDENT', 'ACTIVE', '网络安全专业学生，渗透测试和安全开发'),
('student10', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '郑同学', 'STUDENT', 'ACTIVE', '游戏开发专业学生，Unity和Unreal Engine'),
('student11', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '马同学', 'STUDENT', 'ACTIVE', '区块链技术专业学生，智能合约开发'),
('student12', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '冯同学', 'STUDENT', 'ACTIVE', '物联网工程专业学生，嵌入式开发方向');

-- 插入课程数据
INSERT INTO courses (title, description, cover_image, instructor, category, level, duration, price, original_price, rating, student_count, lesson_count, status, tags, teacher_id) VALUES
-- 前端开发课程
('Vue.js 3.0 完整开发教程', '从零开始学习Vue.js 3.0，包含组合式API、响应式系统、路由管理等核心概念', 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400', '李老师', '前端开发', 'INTERMEDIATE', 1200, 199.00, 299.00, 4.8, 1234, 24, 'PUBLISHED', 'Vue.js,前端,JavaScript,组件化', 2),
('React 现代开发实践', '学习React最新特性，包含Hooks、Context、性能优化等', 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400', '李老师', '前端开发', 'INTERMEDIATE', 1500, 249.00, 349.00, 4.7, 967, 30, 'PUBLISHED', 'React,Hooks,前端,JavaScript', 2),
('JavaScript 高级编程', '深入学习JavaScript高级特性，包含闭包、原型链、异步编程等', 'https://images.unsplash.com/photo-1579468118864-1b9ea3c0db4a?w=400', '李老师', '前端开发', 'ADVANCED', 1800, 299.00, 399.00, 4.9, 2156, 36, 'PUBLISHED', 'JavaScript,高级编程,ES6+,异步', 2),
('TypeScript 从入门到精通', '学习TypeScript语言，提升JavaScript开发体验和代码质量', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400', '李老师', '前端开发', 'INTERMEDIATE', 1000, 179.00, 249.00, 4.6, 876, 20, 'PUBLISHED', 'TypeScript,JavaScript,类型系统', 2),
('CSS3 动画与特效', '掌握CSS3动画、过渡、变换等特效技术，打造炫酷的网页效果', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400', '李老师', '前端开发', 'BEGINNER', 800, 129.00, 179.00, 4.5, 1543, 16, 'PUBLISHED', 'CSS3,动画,特效,响应式', 2),

-- 后端开发课程
('Spring Boot 微服务实战', '深入学习Spring Boot框架，构建企业级微服务应用', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400', '王老师', '后端开发', 'ADVANCED', 1800, 299.00, 399.00, 4.9, 856, 36, 'PUBLISHED', 'Spring Boot,微服务,Java,后端', 3),
('Java 并发编程实战', '深入理解Java并发编程，掌握多线程、线程池、锁机制等', 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400', '王老师', '后端开发', 'ADVANCED', 1600, 279.00, 359.00, 4.8, 654, 32, 'PUBLISHED', 'Java,并发编程,多线程,性能优化', 3),
('Spring Cloud 微服务架构', '学习Spring Cloud生态，构建分布式微服务系统', 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400', '王老师', '后端开发', 'ADVANCED', 2000, 349.00, 449.00, 4.7, 432, 40, 'PUBLISHED', 'Spring Cloud,微服务,分布式,架构', 3),
('MyBatis Plus 数据访问', '掌握MyBatis Plus框架，简化数据库操作和开发效率', 'https://images.unsplash.com/photo-1544383835-bda2bc66a55d?w=400', '张老师', '后端开发', 'INTERMEDIATE', 1200, 199.00, 269.00, 4.6, 789, 24, 'PUBLISHED', 'MyBatis Plus,数据库,ORM,CRUD', 6),

-- 数据科学课程
('Python 数据分析入门', '使用Python进行数据分析，包含pandas、numpy、matplotlib等库的使用', 'https://images.unsplash.com/photo-1526379879527-8559ecfcaec0?w=400', '陈老师', '数据科学', 'BEGINNER', 900, 149.00, 199.00, 4.6, 1456, 18, 'PUBLISHED', 'Python,数据分析,pandas,numpy', 4),
('机器学习算法实战', '从零开始学习机器学习，包含监督学习、无监督学习等算法', 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400', '陈老师', '数据科学', 'INTERMEDIATE', 1800, 299.00, 399.00, 4.8, 987, 36, 'PUBLISHED', '机器学习,算法,Python,scikit-learn', 4),
('深度学习与神经网络', '深入学习深度学习理论和实践，使用TensorFlow和PyTorch', 'https://images.unsplash.com/photo-1507146426996-ef05306b995a?w=400', '陈老师', '数据科学', 'ADVANCED', 2400, 399.00, 499.00, 4.9, 543, 48, 'PUBLISHED', '深度学习,神经网络,TensorFlow,PyTorch', 4),
('数据可视化实战', '使用Python和JavaScript创建交互式数据可视化图表', 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400', '陈老师', '数据科学', 'INTERMEDIATE', 1000, 179.00, 239.00, 4.5, 765, 20, 'PUBLISHED', '数据可视化,Python,D3.js,图表', 4),

-- 移动开发课程
('Android 原生开发', '学习Android原生开发，掌握Java/Kotlin移动应用开发', 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400', '刘老师', '移动开发', 'INTERMEDIATE', 1600, 249.00, 329.00, 4.7, 654, 32, 'PUBLISHED', 'Android,移动开发,Java,Kotlin', 5),
('iOS Swift 开发入门', '从零开始学习iOS开发，使用Swift语言构建iPhone应用', 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400', '刘老师', '移动开发', 'BEGINNER', 1400, 229.00, 299.00, 4.6, 432, 28, 'PUBLISHED', 'iOS,Swift,移动开发,iPhone', 5),
('React Native 跨平台开发', '使用React Native开发跨平台移动应用，一套代码多端运行', 'https://images.unsplash.com/photo-1555774698-0b77e0d5fac6?w=400', '刘老师', '移动开发', 'INTERMEDIATE', 1200, 199.00, 269.00, 4.5, 567, 24, 'PUBLISHED', 'React Native,跨平台,移动开发,JavaScript', 5),
('Flutter 移动应用开发', '学习Google Flutter框架，使用Dart语言开发精美移动应用', 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400', '刘老师', '移动开发', 'INTERMEDIATE', 1500, 239.00, 319.00, 4.8, 789, 30, 'PUBLISHED', 'Flutter,Dart,移动开发,跨平台', 5),

-- 全栈开发课程
('Node.js 全栈开发', '使用Node.js构建全栈应用，包含Express、MongoDB等技术栈', 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400', '李老师', '全栈开发', 'INTERMEDIATE', 1350, 229.00, 299.00, 4.5, 743, 27, 'PUBLISHED', 'Node.js,Express,MongoDB,全栈', 2),
('MERN 全栈项目实战', '使用MongoDB、Express、React、Node.js构建完整的全栈项目', 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400', '李老师', '全栈开发', 'ADVANCED', 2000, 349.00, 449.00, 4.7, 456, 40, 'PUBLISHED', 'MERN,全栈,React,Node.js,MongoDB', 2),

-- 云计算课程
('AWS 云服务实战', '学习Amazon Web Services云平台，掌握云计算核心服务', 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400', '赵老师', '云计算', 'INTERMEDIATE', 1800, 299.00, 399.00, 4.8, 543, 36, 'PUBLISHED', 'AWS,云计算,云服务,架构', 7),
('Docker 容器化技术', '学习Docker容器技术，实现应用的快速部署和扩展', 'https://images.unsplash.com/photo-1605745341112-85968b19335b?w=400', '赵老师', '云计算', 'INTERMEDIATE', 1000, 179.00, 239.00, 4.6, 876, 20, 'PUBLISHED', 'Docker,容器化,DevOps,部署', 7),
('Kubernetes 集群管理', '掌握Kubernetes容器编排技术，管理大规模容器集群', 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400', '赵老师', '云计算', 'ADVANCED', 1600, 279.00, 359.00, 4.7, 321, 32, 'PUBLISHED', 'Kubernetes,容器编排,集群管理,微服务', 7);

-- 插入实验数据
INSERT INTO experiments (title, description, cover_image, subject, difficulty, duration, rating, completed_count, objectives, equipment, steps, tags, status) VALUES
-- 物理实验
('单摆运动实验', '通过改变摆长和初始角度，观察单摆的运动规律，验证单摆周期公式', 'https://images.unsplash.com/photo-1636466497217-26a8cbeaf0aa?w=400', '物理', 'BEGINNER', 30, 4.7, 1234, '理解单摆运动规律,验证周期公式,掌握实验操作方法', '摆球,细线,支架,量角器,秒表', '设置摆长,调整初始角度,释放摆球,测量周期,记录数据,分析结果', '物理,力学,周期运动', 'ACTIVE'),
('光的折射实验', '使用激光笔和半圆形玻璃砖，验证光的折射定律', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400', '物理', 'BEGINNER', 25, 4.6, 756, '验证折射定律,测量折射率,理解光的传播规律', '激光笔,半圆形玻璃砖,量角器,白纸,铅笔', '设置实验装置,调整入射角,观察折射光线,测量角度,记录数据,计算折射率', '物理,光学,折射', 'ACTIVE'),
('自由落体运动实验', '研究物体在重力作用下的自由落体运动规律，测量重力加速度', 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400', '物理', 'INTERMEDIATE', 40, 4.8, 892, '验证自由落体定律,测量重力加速度,分析运动规律', '重物,计时器,测量尺,支架,释放装置', '设置实验装置,调整高度,释放重物,记录时间,计算加速度,误差分析', '物理,力学,运动学', 'ACTIVE'),
('弹簧振子实验', '研究弹簧振子的简谐运动，验证胡克定律和周期公式', 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400', '物理', 'INTERMEDIATE', 35, 4.5, 567, '验证胡克定律,研究简谐运动,测量弹簧常数', '弹簧,重物,支架,刻度尺,秒表', '悬挂弹簧,加载重物,测量伸长量,观察振动,记录周期,计算弹簧常数', '物理,力学,简谐运动', 'ACTIVE'),
('电磁感应实验', '通过线圈和磁铁的相对运动，观察电磁感应现象，验证法拉第定律', 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400', '物理', 'ADVANCED', 50, 4.9, 432, '理解电磁感应原理,验证法拉第定律,测量感应电动势', '线圈,磁铁,电流表,导线,支架', '连接电路,移动磁铁,观察电流变化,改变速度,记录数据,分析规律', '物理,电磁学,感应', 'ACTIVE'),
('光的干涉实验', '使用双缝干涉装置，观察光的波动性质，测量光波波长', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400', '物理', 'ADVANCED', 45, 4.7, 321, '验证光的波动性,观察干涉现象,测量光波波长', '激光器,双缝片,屏幕,测量尺,支架', '调整激光器,安装双缝片,观察干涉条纹,测量条纹间距,计算波长', '物理,光学,波动', 'ACTIVE'),

-- 化学实验
('酸碱中和滴定实验', '使用标准NaOH溶液滴定未知浓度的HCl溶液，学习滴定操作和终点判断', 'https://images.unsplash.com/photo-1532187863486-abf9dbad1b69?w=400', '化学', 'INTERMEDIATE', 45, 4.8, 987, '掌握滴定操作技能,学习终点判断方法,计算未知溶液浓度', '滴定管,锥形瓶,NaOH标准溶液,HCl待测溶液,酚酞指示剂', '准备溶液,装填滴定管,加入指示剂,开始滴定,观察颜色变化,记录消耗体积', '化学,分析化学,滴定', 'ACTIVE'),
('电解水实验', '通过电解水实验，验证水的分子组成，观察氢气和氧气的产生', 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400', '化学', 'BEGINNER', 35, 4.5, 643, '验证水的分子式,观察电解现象,收集和检验气体', '电解器,直流电源,导线,试管,氢氧化钠溶液', '连接电路,加入电解质,通电电解,收集气体,检验气体,分析结果', '化学,电化学,分解反应', 'ACTIVE'),
('金属活动性实验', '通过金属与酸的反应，验证金属活动性顺序', 'https://images.unsplash.com/photo-1554475901-4538ddfbccc2?w=400', '化学', 'BEGINNER', 30, 4.4, 789, '验证金属活动性顺序,观察反应现象,理解置换反应', '锌片,铁片,铜片,稀盐酸,试管,试管架', '准备金属片,加入稀盐酸,观察反应现象,比较反应剧烈程度,得出活动性顺序', '化学,金属,活动性', 'ACTIVE'),
('氧气制备实验', '使用过氧化氢分解制备氧气，学习气体收集方法', 'https://images.unsplash.com/photo-1576086213369-97a306d36557?w=400', '化学', 'BEGINNER', 25, 4.6, 1123, '掌握氧气制备方法,学习气体收集技术,检验氧气性质', '过氧化氢溶液,二氧化锰,试管,导管,集气瓶,木条', '加入催化剂,加热反应,收集气体,检验纯度,观察助燃性', '化学,气体制备,氧化反应', 'ACTIVE'),
('蛋白质检验实验', '使用双缩脲试剂检验蛋白质，观察颜色反应', 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400', '化学', 'INTERMEDIATE', 20, 4.3, 456, '掌握蛋白质检验方法,理解双缩脲反应原理,观察颜色变化', '蛋白质溶液,双缩脲试剂A,双缩脲试剂B,试管,滴管', '加入蛋白质溶液,滴加试剂A,摇匀,滴加试剂B,观察颜色变化', '化学,生物化学,蛋白质', 'ACTIVE'),
('燃烧条件实验', '通过对比实验，验证燃烧的三个条件：可燃物、氧气、达到着火点', 'https://images.unsplash.com/photo-1574169208507-84376144848b?w=400', '化学', 'BEGINNER', 30, 4.5, 678, '理解燃烧条件,设计对比实验,观察燃烧现象', '蜡烛,火柴,烧杯,石灰水,玻璃片', '点燃蜡烛,罩上烧杯,观察熄灭,分析原因,验证燃烧条件', '化学,燃烧,氧化反应', 'ACTIVE'),

-- 生物实验
('显微镜观察细胞', '使用显微镜观察植物细胞和动物细胞的结构差异', 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400', '生物', 'BEGINNER', 40, 4.6, 892, '掌握显微镜使用方法,观察细胞结构,区分植物和动物细胞', '显微镜,载玻片,盖玻片,洋葱表皮,口腔上皮细胞,碘液', '制作临时装片,调节显微镜,观察细胞,绘制结构图,比较差异', '生物,细胞,显微镜', 'ACTIVE'),
('DNA提取实验', '从香蕉中提取DNA，观察DNA的物理性质', 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400', '生物', 'INTERMEDIATE', 50, 4.7, 543, '学习DNA提取方法,观察DNA结构,理解遗传物质性质', '香蕉,洗洁精,食盐,酒精,搅拌棒,烧杯', '捣碎香蕉,加入提取液,过滤,加入酒精,观察DNA析出', '生物,遗传学,DNA', 'ACTIVE'),
('酶活性实验', '研究温度对酶活性的影响，使用过氧化氢酶分解过氧化氢', 'https://images.unsplash.com/photo-1576086213369-97a306d36557?w=400', '生物', 'INTERMEDIATE', 35, 4.8, 321, '理解酶的特性,研究温度对酶活性的影响,观察反应速率', '过氧化氢溶液,新鲜肝脏,试管,温度计,水浴锅', '准备不同温度环境,加入酶溶液,观察反应速率,记录数据,分析结果', '生物,酶学,生物化学', 'ACTIVE');

-- 插入帖子数据
INSERT INTO posts (title, content, category, tags, view_count, like_count, comment_count, is_pinned, status, author_id) VALUES
-- 学习交流类帖子
('Vue 3.0 学习心得分享', '最近在学习Vue 3.0，感觉组合式API真的很强大，特别是在逻辑复用方面。想和大家分享一些学习心得。首先是响应式系统的改进，ref和reactive的使用让状态管理更加灵活。其次是组合式API让逻辑复用变得非常简单，不再需要复杂的mixin。最后是性能优化，Vue 3的渲染性能比Vue 2提升了很多。', '学习交流', 'Vue.js,前端,学习心得', 1234, 89, 23, TRUE, 'PUBLISHED', 9),
('如何高效学习编程？', '作为一个从零开始学编程的学生，想分享一些我认为比较有效的学习方法和经验。第一，要有明确的学习目标和计划。第二，理论学习和实践相结合，多动手写代码。第三，遇到问题要善于思考和查找资料。第四，加入学习社区，与其他学习者交流。第五，坚持不懈，编程学习是一个长期的过程。', '学习交流', '学习方法,编程,经验分享', 1567, 98, 31, FALSE, 'PUBLISHED', 10),
('Python数据分析学习路线', '想学习Python数据分析的同学可以参考这个学习路线：1.Python基础语法 2.NumPy数组操作 3.Pandas数据处理 4.Matplotlib数据可视化 5.机器学习算法 6.实际项目练习。每个阶段都要多练习，理论结合实践才能真正掌握。', '学习交流', 'Python,数据分析,学习路线', 892, 67, 19, FALSE, 'PUBLISHED', 11),
('算法刷题心得体会', '刷了半年的算法题，总结一些心得：1.先掌握基础数据结构 2.分类刷题，每种类型都要练习 3.不要只看答案，要自己思考 4.定期复习之前做过的题 5.参加在线比赛提升能力。坚持下去，算法能力一定会有提升。', '学习交流', '算法,刷题,数据结构', 1345, 78, 25, FALSE, 'PUBLISHED', 12),
('大学生如何规划编程学习', '作为计算机专业的学生，分享一下我的学习规划：大一打好基础（C语言、数据结构），大二学习专业课程（操作系统、计算机网络），大三选择方向深入学习（前端、后端、算法等），大四准备实习和毕业设计。重要的是要多实践，多做项目。', '学习交流', '大学生,编程,学习规划', 756, 45, 12, FALSE, 'PUBLISHED', 13),

-- 技术讨论类帖子
('Spring Boot 项目实战经验', '在做毕业设计的过程中，使用Spring Boot构建了一个完整的后端系统，遇到了一些问题，也积累了一些经验。比如数据库连接池的配置、异常处理的统一管理、JWT认证的实现、接口文档的自动生成等。这些都是实际项目中会遇到的问题，分享给大家参考。', '技术讨论', 'Spring Boot,后端,项目实战', 987, 67, 18, FALSE, 'PUBLISHED', 10),
('React Hooks 最佳实践', '使用React Hooks已经有一段时间了，总结了一些最佳实践和常见的坑：1.useState的正确使用方式 2.useEffect的依赖数组管理 3.自定义Hook的设计原则 4.性能优化技巧 5.常见错误避免。希望对正在学习React的同学有帮助。', '技术讨论', 'React,Hooks,最佳实践', 876, 54, 16, FALSE, 'PUBLISHED', 9),
('微服务架构设计思考', '最近在学习微服务架构，分享一些思考：微服务不是银弹，要根据业务复杂度选择。服务拆分要合理，避免过度拆分。要考虑服务间通信、数据一致性、监控等问题。技术选型要慎重，团队能力也很重要。', '技术讨论', '微服务,架构设计,分布式', 654, 43, 11, FALSE, 'PUBLISHED', 11),
('前端性能优化实践', '总结一些前端性能优化的实践经验：1.代码分割和懒加载 2.图片优化和CDN使用 3.缓存策略设计 4.打包优化配置 5.运行时性能监控。这些技术在实际项目中都很有用，可以显著提升用户体验。', '技术讨论', '前端,性能优化,用户体验', 1123, 89, 27, FALSE, 'PUBLISHED', 12),
('数据库设计原则分享', '在项目开发中，数据库设计非常重要。分享一些设计原则：1.遵循范式设计 2.合理使用索引 3.考虑查询性能 4.预留扩展空间 5.做好数据备份。好的数据库设计是系统稳定运行的基础。', '技术讨论', '数据库,设计原则,MySQL', 543, 32, 8, FALSE, 'PUBLISHED', 13),

-- 求职就业类帖子
('前端面试题总结', '最近在准备前端面试，整理了一些常见的面试题：JavaScript基础（闭包、原型链、异步编程）、CSS布局（Flex、Grid、响应式）、框架相关（Vue/React生命周期、状态管理）、工程化（Webpack、性能优化）、算法题（常见排序、树遍历）。希望对准备面试的同学有帮助。', '求职就业', '面试,前端,JavaScript', 2156, 145, 42, TRUE, 'PUBLISHED', 9),
('后端开发面试经验', '分享一下后端面试的经验：技术方面要掌握Java基础、Spring框架、数据库、缓存、消息队列等。项目经验很重要，要能清楚描述自己做过的项目。算法题也要准备，虽然不是重点但也会考。最重要的是要有学习能力和解决问题的思路。', '求职就业', '面试,后端,Java', 1432, 87, 29, FALSE, 'PUBLISHED', 10),
('实习生求职攻略', '作为刚找到实习的学生，分享一些求职经验：1.简历要突出项目经验 2.技术栈要有深度不要只是了解 3.面试要诚实，不会的就说不会 4.要展现学习能力和解决问题的思路 5.多投简历，机会总会有的。加油！', '求职就业', '实习,求职,简历', 987, 56, 15, FALSE, 'PUBLISHED', 11),
('转行程序员的心路历程', '从非计算机专业转行做程序员，分享一下心路历程：刚开始很迷茫，不知道从哪里学起。后来制定了学习计划，每天坚持学习。遇到困难时想过放弃，但最终坚持下来了。现在已经找到工作，感谢这段经历让我成长。', '求职就业', '转行,程序员,经历分享', 1234, 78, 22, FALSE, 'PUBLISHED', 12),

-- 项目展示类帖子
('个人博客系统开发记录', '用Vue3+Spring Boot开发了一个个人博客系统，功能包括文章发布、评论系统、用户管理、标签分类等。前端使用了Element Plus组件库，后端使用了MyBatis Plus。整个开发过程学到了很多，也遇到了不少问题，记录下来分享给大家。', '项目展示', '博客系统,Vue3,Spring Boot', 876, 65, 18, FALSE, 'PUBLISHED', 13),
('在线教育平台项目分享', '和同学一起开发了一个在线教育平台，包含课程管理、视频播放、用户学习进度跟踪等功能。技术栈使用React+Node.js+MongoDB。项目虽然不大，但涵盖了前后端开发的各个方面，是很好的学习实践。', '项目展示', '教育平台,React,Node.js', 654, 43, 12, FALSE, 'PUBLISHED', 14),
('移动端商城App开发', '使用React Native开发了一个移动端商城App，实现了商品浏览、购物车、订单管理、支付等功能。跨平台开发确实很方便，一套代码可以同时运行在iOS和Android上。分享一些开发中的经验和踩过的坑。', '项目展示', '移动开发,React Native,商城', 543, 38, 9, FALSE, 'PUBLISHED', 15),

-- 技术资讯类帖子
('2024年前端技术趋势', '总结一下2024年前端技术的发展趋势：1.框架方面Vue3和React18继续发展 2.构建工具Vite越来越流行 3.TypeScript使用率持续上升 4.微前端架构逐渐成熟 5.WebAssembly应用增多。技术发展很快，要保持学习的心态。', '技术资讯', '前端,技术趋势,2024', 1567, 98, 31, TRUE, 'PUBLISHED', 16),
('人工智能在编程中的应用', 'AI技术在编程领域的应用越来越广泛：代码自动生成、智能代码补全、bug检测、代码审查等。GitHub Copilot、ChatGPT等工具已经成为很多程序员的助手。但AI不会完全替代程序员，而是会改变我们的工作方式。', '技术资讯', 'AI,人工智能,编程', 1234, 76, 24, FALSE, 'PUBLISHED', 17),
('云原生技术发展现状', '云原生技术发展迅速，容器化、微服务、DevOps已经成为主流。Kubernetes成为容器编排的标准，服务网格技术也在快速发展。对于开发者来说，掌握这些技术变得越来越重要。', '技术资讯', '云原生,Kubernetes,微服务', 892, 54, 16, FALSE, 'PUBLISHED', 18);

-- 插入学习小组数据
INSERT INTO study_groups (name, description, cover_image, category, max_members, current_members, is_public, tags, status, creator_id) VALUES
-- 技术学习小组
('前端技术交流群', '专注于前端技术讨论和学习，欢迎Vue、React、Angular爱好者加入。定期分享最新技术动态，组织代码review和项目实战', 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=200', '前端开发', 100, 67, TRUE, '前端,Vue,React,技术交流', 'ACTIVE', 9),
('Java后端开发小组', 'Java后端技术学习小组，分享Spring Boot、微服务等技术经验。从基础到进阶，一起成长', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=200', '后端开发', 80, 54, TRUE, 'Java,Spring Boot,后端,微服务', 'ACTIVE', 10),
('Python数据分析小组', '学习Python在数据分析领域的应用，包括pandas、numpy、机器学习等。理论与实践并重', 'https://images.unsplash.com/photo-1526379879527-8559ecfcaec0?w=200', '数据科学', 60, 43, TRUE, 'Python,数据分析,pandas,numpy', 'ACTIVE', 11),
('算法与数据结构', '一起刷题，一起进步！专注算法和数据结构的学习讨论。每周组织刷题活动，分享解题思路', 'https://images.unsplash.com/photo-1509966756634-9c23dd6e6815?w=200', '算法', 150, 89, TRUE, '算法,数据结构,刷题,面试', 'ACTIVE', 12),
('全栈开发者联盟', '全栈开发技术交流，涵盖前端、后端、数据库等各个方面。适合想要全面发展的开发者', 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=200', '全栈开发', 120, 76, TRUE, '全栈,前端,后端,数据库', 'ACTIVE', 13),

-- 移动开发小组
('Android开发交流群', 'Android原生开发技术交流，分享Kotlin、Jetpack等最新技术。欢迎初学者和有经验的开发者', 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=200', '移动开发', 80, 45, TRUE, 'Android,Kotlin,移动开发,Jetpack', 'ACTIVE', 14),
('iOS Swift开发小组', 'iOS开发技术学习小组，使用Swift语言开发iPhone应用。分享开发经验和最佳实践', 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=200', '移动开发', 70, 32, TRUE, 'iOS,Swift,移动开发,iPhone', 'ACTIVE', 15),
('跨平台开发联盟', '专注于React Native、Flutter等跨平台开发技术。一套代码，多端运行', 'https://images.unsplash.com/photo-1555774698-0b77e0d5fac6?w=200', '移动开发', 90, 56, TRUE, '跨平台,React Native,Flutter,移动开发', 'ACTIVE', 16),

-- 专业方向小组
('机器学习研究组', '深入学习机器学习和深度学习技术，分享论文阅读和项目实战经验。适合有一定基础的学习者', 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=200', '人工智能', 50, 28, TRUE, '机器学习,深度学习,AI,TensorFlow', 'ACTIVE', 17),
('云计算技术小组', '学习AWS、阿里云等云平台技术，掌握Docker、Kubernetes等容器技术。面向云原生开发', 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=200', '云计算', 60, 34, TRUE, '云计算,AWS,Docker,Kubernetes', 'ACTIVE', 18),
('网络安全研究组', '网络安全技术学习和研究，包括渗透测试、安全开发、密码学等。提升安全意识和技能', 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=200', '网络安全', 40, 23, TRUE, '网络安全,渗透测试,安全开发,密码学', 'ACTIVE', 19),
('区块链技术探索', '探索区块链技术和应用，学习智能合约开发。了解去中心化应用的设计和实现', 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=200', '区块链', 35, 18, TRUE, '区块链,智能合约,去中心化,Web3', 'ACTIVE', 20),

-- 学习交流小组
('编程新手互助群', '专为编程初学者设计的互助小组，大家一起学习基础知识，解答疑问，共同进步', 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=200', '新手入门', 200, 134, TRUE, '新手,编程入门,互助,基础', 'ACTIVE', 21),
('大学生编程社团', '面向在校大学生的编程学习社团，组织技术分享、项目合作、竞赛参与等活动', 'https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=200', '学生社团', 150, 98, TRUE, '大学生,编程社团,竞赛,项目合作', 'ACTIVE', 22),
('职场程序员交流', '面向在职程序员的交流平台，分享工作经验、技术选型、职业发展等话题', 'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=200', '职场交流', 100, 67, TRUE, '职场,程序员,经验分享,职业发展', 'ACTIVE', 23),
('女性程序员联盟', '为女性程序员提供交流和支持的平台，鼓励更多女性参与到编程领域中来', 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=200', '多元化', 80, 45, TRUE, '女性程序员,多元化,支持,鼓励', 'ACTIVE', 24),

-- 项目实战小组
('开源项目贡献组', '参与开源项目，学习协作开发，提升代码质量和项目经验。欢迎各个技术栈的开发者', 'https://images.unsplash.com/photo-1556075798-4825dfaaf498?w=200', '开源项目', 70, 41, TRUE, '开源,项目贡献,协作开发,GitHub', 'ACTIVE', 25),
('创业项目孵化器', '有创业想法的程序员聚集地，讨论项目创意，寻找合作伙伴，分享创业经验', 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=200', '创业项目', 50, 29, TRUE, '创业,项目孵化,合作伙伴,创新', 'ACTIVE', 26);

-- 插入新闻数据
INSERT INTO news (title, summary, content, cover_image, category, tags, source, author, view_count, like_count, is_hot, status, published_at) VALUES
-- 前端技术新闻
('Vue 3.4 正式发布，带来重大性能提升', 'Vue.js 团队发布了 3.4 版本，在编译器优化、响应式系统等方面带来显著改进', 'Vue.js 3.4 版本正式发布，这个版本在编译器优化、响应式系统、开发者体验等方面带来了重大改进。新版本的渲染性能提升了15%，内存使用减少了10%，同时引入了更多的开发者友好特性。', 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400', '前端技术', 'Vue.js,前端,更新', 'Vue官方', 'Vue团队', 2345, 156, TRUE, 'PUBLISHED', '2024-01-15 10:00:00'),
('React 18.3 发布，修复关键Bug', 'React团队发布18.3版本，修复了多个关键bug，提升了开发体验', 'React 18.3版本主要专注于bug修复和稳定性提升，解决了并发渲染中的一些边缘情况问题，同时优化了开发工具的性能。', 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400', '前端技术', 'React,前端,更新', 'React官方', 'React团队', 1876, 123, TRUE, 'PUBLISHED', '2024-01-12 14:20:00'),
('Vite 5.0 正式发布，构建速度再提升', 'Vite 5.0带来了更快的构建速度和更好的开发体验', 'Vite 5.0版本在构建性能、插件生态、开发服务器等方面都有显著提升。新版本的冷启动速度提升了30%，热更新速度提升了50%。', 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400', '前端技术', 'Vite,构建工具,性能', 'Vite官方', 'Vite团队', 1543, 98, FALSE, 'PUBLISHED', '2024-01-08 11:30:00'),

-- 后端技术新闻
('Spring Boot 3.2 发布，支持虚拟线程', 'Spring Boot 3.2 版本发布，正式支持 Java 21 的虚拟线程特性，大幅提升并发性能', 'Spring Boot 3.2版本带来了对Java 21虚拟线程的完整支持，这将大幅提升应用的并发处理能力。同时还引入了更多的自动配置和性能优化。', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400', '后端技术', 'Spring Boot,Java,虚拟线程', 'Spring官方', 'Spring团队', 1876, 98, TRUE, 'PUBLISHED', '2024-01-10 14:30:00'),
('Java 21 LTS 正式发布', 'Java 21作为新的长期支持版本正式发布，带来虚拟线程等重要特性', 'Java 21 LTS版本正式发布，引入了虚拟线程、模式匹配、记录模式等重要特性。这是继Java 17之后的新一代长期支持版本。', 'https://images.unsplash.com/photo-1544383835-bda2bc66a55d?w=400', '后端技术', 'Java,LTS,虚拟线程', 'Oracle官方', 'Java团队', 2134, 145, TRUE, 'PUBLISHED', '2024-01-05 09:00:00'),
('Node.js 21 发布，性能大幅提升', 'Node.js 21版本发布，V8引擎升级，性能和稳定性都有显著提升', 'Node.js 21版本升级了V8引擎到最新版本，JavaScript执行性能提升了20%，同时引入了更多的实验性特性和API改进。', 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400', '后端技术', 'Node.js,性能,V8', 'Node.js官方', 'Node.js团队', 1432, 87, FALSE, 'PUBLISHED', '2024-01-03 16:45:00'),

-- 数据库技术新闻
('MySQL 8.0 新特性详解', 'MySQL 8.0 版本带来了众多新特性，包括窗口函数、CTE、JSON增强等', 'MySQL 8.0是一个里程碑式的版本，引入了窗口函数、公用表表达式(CTE)、JSON增强功能、角色管理等重要特性，大幅提升了数据库的功能和性能。', 'https://images.unsplash.com/photo-1544383835-bda2bc66a55d?w=400', '数据库技术', 'MySQL,数据库,新特性', 'MySQL官方', 'MySQL团队', 987, 45, FALSE, 'PUBLISHED', '2024-01-03 11:45:00'),
('PostgreSQL 16 发布，查询性能提升', 'PostgreSQL 16版本发布，在查询优化和并行处理方面有重大改进', 'PostgreSQL 16版本在查询优化器、并行查询、逻辑复制等方面都有显著改进。复杂查询的性能平均提升了25%。', 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400', '数据库技术', 'PostgreSQL,数据库,性能', 'PostgreSQL官方', 'PostgreSQL团队', 876, 56, FALSE, 'PUBLISHED', '2024-01-01 10:20:00'),

-- 人工智能新闻
('ChatGPT-4 Turbo 发布，成本降低性能提升', 'OpenAI发布ChatGPT-4 Turbo版本，成本降低3倍，上下文长度增加到128K', 'ChatGPT-4 Turbo版本在保持高质量输出的同时，大幅降低了使用成本，上下文长度也从32K增加到128K，能够处理更长的文档和对话。', 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400', '人工智能', 'ChatGPT,AI,OpenAI', 'OpenAI官方', 'OpenAI团队', 3456, 234, TRUE, 'PUBLISHED', '2024-01-18 15:00:00'),
('GitHub Copilot Chat 正式发布', 'GitHub推出Copilot Chat功能，为开发者提供对话式AI编程助手', 'GitHub Copilot Chat正式发布，开发者可以通过自然语言与AI助手对话，获得代码建议、解释和调试帮助，大幅提升开发效率。', 'https://images.unsplash.com/photo-1556075798-4825dfaaf498?w=200', '人工智能', 'GitHub,Copilot,AI编程', 'GitHub官方', 'GitHub团队', 2345, 167, TRUE, 'PUBLISHED', '2024-01-16 12:30:00'),

-- 行业动态新闻
('2024年前端开发趋势预测', '分析2024年前端开发的主要趋势，包括框架发展、工具链演进等', '2024年前端开发将继续快速发展，本文分析了几个主要趋势：组件化开发成熟、构建工具优化、TypeScript普及、微前端架构发展、WebAssembly应用增多。', 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400', '行业动态', '前端,趋势,2024', 'TechNews', '技术编辑', 1234, 67, FALSE, 'PUBLISHED', '2024-01-08 09:15:00'),
('云原生技术发展报告', '2024年云原生技术发展现状和趋势分析', '云原生技术在2024年继续快速发展，容器化、微服务、DevOps已成为主流。Kubernetes生态更加成熟，服务网格技术快速普及。', 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400', '行业动态', '云原生,Kubernetes,微服务', 'CloudNative', '云计算专家', 1567, 89, FALSE, 'PUBLISHED', '2024-01-06 14:20:00'),
('开源软件发展趋势', '分析开源软件在2024年的发展趋势和商业模式变化', '开源软件继续蓬勃发展，越来越多的企业采用开源优先策略。AI驱动的开源项目增多，开源商业化模式也在不断创新。', 'https://images.unsplash.com/photo-1556075798-4825dfaaf498?w=400', '行业动态', '开源,软件发展,商业模式', 'OpenSource', '开源专家', 1123, 78, FALSE, 'PUBLISHED', '2024-01-04 11:10:00'),

-- 教育科技新闻
('人工智能在教育领域的应用前景', '探讨AI技术如何改变传统教育模式，提升学习效率和个性化体验', '人工智能技术正在深刻改变教育行业，个性化学习、智能辅导、自动评估等应用场景不断涌现，为教育带来了新的可能性。', 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400', '教育科技', 'AI,教育,技术', 'EduTech', '教育专家', 1654, 89, FALSE, 'PUBLISHED', '2024-01-05 16:20:00'),
('在线教育平台技术架构演进', '分析主流在线教育平台的技术架构设计和演进历程', '在线教育平台在技术架构方面不断演进，从单体架构到微服务架构，从传统部署到云原生部署，技术选型和架构设计越来越成熟。', 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400', '教育科技', '在线教育,架构设计,技术演进', 'EduTech', '架构师', 987, 54, FALSE, 'PUBLISHED', '2024-01-02 13:40:00'),

-- 移动开发新闻
('Flutter 3.16 发布，性能再次提升', 'Flutter 3.16版本发布，在渲染性能和开发体验方面都有显著改进', 'Flutter 3.16版本在渲染引擎、开发工具、平台支持等方面都有重要更新。新版本的渲染性能提升了20%，支持更多的原生平台特性。', 'https://images.unsplash.com/photo-1555774698-0b77e0d5fac6?w=400', '移动开发', 'Flutter,移动开发,跨平台', 'Flutter官方', 'Flutter团队', 1345, 87, FALSE, 'PUBLISHED', '2024-01-07 10:15:00'),
('React Native 0.73 发布', 'React Native新版本发布，带来更好的性能和开发体验', 'React Native 0.73版本在性能优化、开发工具、平台兼容性等方面都有改进。新架构的稳定性进一步提升，开发体验更加流畅。', 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400', '移动开发', 'React Native,移动开发,跨平台', 'React Native官方', 'RN团队', 1123, 76, FALSE, 'PUBLISHED', '2024-01-04 15:25:00');

-- 插入学习记录数据
INSERT INTO learning_records (user_id, course_id, progress, last_position, completed_at) VALUES
-- 张同学(student1)的学习记录
(9, 1, 75, 18, NULL),  -- 张同学学习Vue课程，进度75%
(9, 2, 30, 9, NULL),   -- 张同学学习React课程，进度30%
(9, 22, 100, 27, '2024-01-20 15:30:00'),  -- 张同学完成Node.js课程
(9, 3, 15, 3, NULL),   -- 张同学学习JavaScript高级编程，进度15%
(9, 5, 60, 12, NULL),  -- 张同学学习CSS3动画，进度60%

-- 李同学(student2)的学习记录
(10, 6, 90, 32, NULL),  -- 李同学学习Spring Boot课程，进度90%
(10, 1, 45, 11, NULL),  -- 李同学学习Vue课程，进度45%
(10, 11, 100, 18, '2024-01-18 14:20:00'),  -- 李同学完成Python数据分析课程
(10, 7, 80, 28, NULL),  -- 李同学学习Java并发编程，进度80%
(10, 9, 25, 5, NULL),   -- 李同学学习MyBatis Plus，进度25%

-- 王同学(student3)的学习记录
(11, 11, 85, 15, NULL), -- 王同学学习Python数据分析，进度85%
(11, 12, 40, 14, NULL), -- 王同学学习机器学习，进度40%
(11, 13, 20, 9, NULL),  -- 王同学学习深度学习，进度20%
(11, 14, 70, 14, NULL), -- 王同学学习数据可视化，进度70%

-- 陈同学(student4)的学习记录
(12, 15, 55, 17, NULL), -- 陈同学学习Android开发，进度55%
(12, 17, 35, 8, NULL),  -- 陈同学学习React Native，进度35%
(12, 1, 100, 24, '2024-01-22 16:45:00'), -- 陈同学完成Vue课程
(12, 2, 65, 19, NULL),  -- 陈同学学习React，进度65%

-- 刘同学(student5)的学习记录
(13, 22, 90, 35, NULL), -- 刘同学学习Node.js全栈开发，进度90%
(13, 23, 50, 20, NULL), -- 刘同学学习MERN全栈项目，进度50%
(13, 1, 100, 24, '2024-01-19 11:20:00'), -- 刘同学完成Vue课程
(13, 6, 30, 10, NULL),  -- 刘同学学习Spring Boot，进度30%

-- 赵同学(student6)的学习记录
(14, 24, 75, 27, NULL), -- 赵同学学习AWS云服务，进度75%
(14, 25, 60, 12, NULL), -- 赵同学学习Docker，进度60%
(14, 26, 40, 12, NULL), -- 赵同学学习Kubernetes，进度40%
(14, 6, 85, 30, NULL),  -- 赵同学学习Spring Boot，进度85%

-- 孙同学(student7)的学习记录
(15, 12, 95, 34, NULL), -- 孙同学学习机器学习，进度95%
(15, 13, 70, 33, NULL), -- 孙同学学习深度学习，进度70%
(15, 11, 100, 18, '2024-01-17 14:30:00'), -- 孙同学完成Python数据分析
(15, 14, 80, 16, NULL), -- 孙同学学习数据可视化，进度80%

-- 周同学(student8)的学习记录
(16, 15, 80, 25, NULL), -- 周同学学习Android开发，进度80%
(16, 18, 45, 13, NULL), -- 周同学学习Flutter，进度45%
(16, 3, 60, 21, NULL),  -- 周同学学习JavaScript高级编程，进度60%
(16, 4, 100, 20, '2024-01-21 10:15:00'), -- 周同学完成TypeScript课程

-- 吴同学(student9)的学习记录
(17, 6, 70, 25, NULL),  -- 吴同学学习Spring Boot，进度70%
(17, 8, 55, 22, NULL),  -- 吴同学学习Spring Cloud，进度55%
(17, 7, 40, 12, NULL),  -- 吴同学学习Java并发编程，进度40%

-- 郑同学(student10)的学习记录
(18, 17, 85, 20, NULL), -- 郑同学学习React Native，进度85%
(18, 18, 60, 18, NULL), -- 郑同学学习Flutter，进度60%
(18, 2, 100, 30, '2024-01-16 13:25:00'), -- 郑同学完成React课程
(18, 1, 75, 18, NULL),  -- 郑同学学习Vue课程，进度75%

-- 马同学(student11)的学习记录
(19, 24, 50, 18, NULL), -- 马同学学习AWS云服务，进度50%
(19, 25, 80, 16, NULL), -- 马同学学习Docker，进度80%
(19, 6, 65, 23, NULL),  -- 马同学学习Spring Boot，进度65%

-- 冯同学(student12)的学习记录
(20, 15, 40, 12, NULL), -- 冯同学学习Android开发，进度40%
(20, 3, 55, 19, NULL),  -- 冯同学学习JavaScript高级编程，进度55%
(20, 1, 90, 21, NULL),  -- 冯同学学习Vue课程，进度90%

-- 教师作为学习者的记录
(2, 1, 100, 24, '2024-01-15 10:00:00'),  -- 李老师完成Vue课程（作为学习者）
(2, 2, 100, 30, '2024-01-12 16:30:00'),  -- 李老师完成React课程
(2, 4, 85, 17, NULL),   -- 李老师学习TypeScript，进度85%
(3, 2, 60, 18, NULL),   -- 王老师学习React课程，进度60%
(3, 11, 75, 13, NULL),  -- 王老师学习Python数据分析，进度75%
(3, 24, 90, 32, NULL),  -- 王老师学习AWS云服务，进度90%
(4, 12, 80, 28, NULL),  -- 陈老师学习机器学习，进度80%
(4, 13, 95, 45, NULL),  -- 陈老师学习深度学习，进度95%
(5, 15, 70, 22, NULL),  -- 刘老师学习Android开发，进度70%
(5, 17, 85, 20, NULL),  -- 刘老师学习React Native，进度85%
(6, 6, 100, 36, '2024-01-14 09:45:00'),  -- 张老师完成Spring Boot课程
(6, 9, 100, 24, '2024-01-11 14:20:00'),  -- 张老师完成MyBatis Plus课程
(7, 24, 100, 36, '2024-01-13 11:30:00'), -- 赵老师完成AWS云服务课程
(7, 25, 100, 20, '2024-01-10 15:15:00'); -- 赵老师完成Docker课程

-- ========================================
-- 数据库脚本执行完成
-- ========================================

-- 查看数据统计
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'courses', COUNT(*) FROM courses
UNION ALL
SELECT 'experiments', COUNT(*) FROM experiments
UNION ALL
SELECT 'posts', COUNT(*) FROM posts
UNION ALL
SELECT 'study_groups', COUNT(*) FROM study_groups
UNION ALL
SELECT 'news', COUNT(*) FROM news
UNION ALL
SELECT 'learning_records', COUNT(*) FROM learning_records;
