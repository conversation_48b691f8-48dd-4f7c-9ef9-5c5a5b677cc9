-- DIV教育学习平台 - 完整MySQL数据库脚本
-- 包含7张核心表和完整示例数据
-- MySQL 8.0+ 兼容

-- 创建数据库
CREATE DATABASE IF NOT EXISTS div_education 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE div_education;

-- ========================================
-- 1. 用户表
-- ========================================
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    role VARCHAR(20) NOT NULL DEFAULT 'STUDENT' COMMENT '角色：ADMIN/TEACHER/STUDENT',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE/INACTIVE/BANNED',
    bio VARCHAR(500) COMMENT '个人简介',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ========================================
-- 2. 课程表
-- ========================================
CREATE TABLE IF NOT EXISTS courses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '课程标题',
    description TEXT COMMENT '课程描述',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    instructor VARCHAR(100) NOT NULL COMMENT '讲师姓名',
    category VARCHAR(50) NOT NULL COMMENT '课程分类',
    level VARCHAR(20) NOT NULL DEFAULT 'BEGINNER' COMMENT '难度等级：BEGINNER/INTERMEDIATE/ADVANCED',
    duration INTEGER NOT NULL DEFAULT 0 COMMENT '课程时长（分钟）',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '课程价格',
    original_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '原价',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分（0-5）',
    student_count INTEGER NOT NULL DEFAULT 0 COMMENT '学生数量',
    lesson_count INTEGER NOT NULL DEFAULT 0 COMMENT '课时数量',
    status VARCHAR(20) NOT NULL DEFAULT 'PUBLISHED' COMMENT '状态：DRAFT/PUBLISHED/ARCHIVED',
    tags VARCHAR(500) COMMENT '标签（逗号分隔）',
    teacher_id BIGINT NOT NULL COMMENT '教师ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_teacher (teacher_id),
    INDEX idx_rating (rating),
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程表';

-- ========================================
-- 3. 实验表
-- ========================================
CREATE TABLE IF NOT EXISTS experiments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '实验标题',
    description TEXT COMMENT '实验描述',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    subject VARCHAR(50) NOT NULL COMMENT '学科',
    difficulty VARCHAR(20) NOT NULL DEFAULT 'BEGINNER' COMMENT '难度：BEGINNER/INTERMEDIATE/ADVANCED',
    duration INTEGER NOT NULL DEFAULT 0 COMMENT '实验时长（分钟）',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分（0-5）',
    completed_count INTEGER NOT NULL DEFAULT 0 COMMENT '完成人数',
    objectives TEXT COMMENT '实验目标（逗号分隔）',
    equipment TEXT COMMENT '实验器材（逗号分隔）',
    steps TEXT COMMENT '实验步骤（逗号分隔）',
    tags TEXT COMMENT '标签（逗号分隔）',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE/INACTIVE/MAINTENANCE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_subject (subject),
    INDEX idx_difficulty (difficulty),
    INDEX idx_status (status),
    INDEX idx_rating (rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实验表';

-- ========================================
-- 4. 帖子表
-- ========================================
CREATE TABLE IF NOT EXISTS posts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '帖子标题',
    content TEXT NOT NULL COMMENT '帖子内容',
    category VARCHAR(50) NOT NULL COMMENT '分类',
    tags TEXT COMMENT '标签（逗号分隔）',
    view_count INTEGER NOT NULL DEFAULT 0 COMMENT '浏览数',
    like_count INTEGER NOT NULL DEFAULT 0 COMMENT '点赞数',
    comment_count INTEGER NOT NULL DEFAULT 0 COMMENT '评论数',
    is_pinned BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否置顶',
    status VARCHAR(20) NOT NULL DEFAULT 'PUBLISHED' COMMENT '状态：DRAFT/PUBLISHED/HIDDEN/DELETED',
    author_id BIGINT NOT NULL COMMENT '作者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_author (author_id),
    INDEX idx_pinned (is_pinned),
    INDEX idx_created (created_at),
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子表';

-- ========================================
-- 5. 学习小组表
-- ========================================
CREATE TABLE IF NOT EXISTS study_groups (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '小组名称',
    description TEXT COMMENT '小组描述',
    cover_image VARCHAR(500) COMMENT '小组封面',
    category VARCHAR(50) COMMENT '分类',
    max_members INTEGER DEFAULT 100 COMMENT '最大成员数',
    current_members INTEGER DEFAULT 0 COMMENT '当前成员数',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    tags TEXT COMMENT '标签（逗号分隔）',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE/INACTIVE/ARCHIVED',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_creator (creator_id),
    INDEX idx_public (is_public),
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习小组表';

-- ========================================
-- 6. 新闻表
-- ========================================
CREATE TABLE IF NOT EXISTS news (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '新闻标题',
    summary TEXT COMMENT '新闻摘要',
    content TEXT COMMENT '新闻内容',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    category VARCHAR(50) COMMENT '分类',
    tags TEXT COMMENT '标签（逗号分隔）',
    source VARCHAR(100) COMMENT '来源',
    author VARCHAR(100) COMMENT '作者',
    view_count INTEGER DEFAULT 0 COMMENT '浏览数',
    like_count INTEGER DEFAULT 0 COMMENT '点赞数',
    is_hot BOOLEAN DEFAULT FALSE COMMENT '是否热门',
    status VARCHAR(20) NOT NULL DEFAULT 'PUBLISHED' COMMENT '状态：DRAFT/PUBLISHED/ARCHIVED',
    published_at TIMESTAMP NULL COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_hot (is_hot),
    INDEX idx_published (published_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻表';

-- ========================================
-- 7. 学习记录表
-- ========================================
CREATE TABLE IF NOT EXISTS learning_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    course_id BIGINT NOT NULL COMMENT '课程ID',
    progress INTEGER DEFAULT 0 COMMENT '学习进度（百分比）',
    last_position INTEGER DEFAULT 0 COMMENT '最后学习位置',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_user_course (user_id, course_id),
    INDEX idx_user (user_id),
    INDEX idx_course (course_id),
    INDEX idx_progress (progress),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习记录表';

-- ========================================
-- 插入示例数据
-- ========================================

-- 插入用户数据（密码：123456，已BCrypt加密）
INSERT INTO users (username, email, password, nickname, role, status, bio) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '系统管理员', 'ADMIN', 'ACTIVE', '系统管理员账号'),
('teacher1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '李老师', 'TEACHER', 'ACTIVE', '前端开发讲师，拥有10年开发经验'),
('teacher2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '王老师', 'TEACHER', 'ACTIVE', '后端开发专家，Java技术栈资深讲师'),
('student1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '张同学', 'STUDENT', 'ACTIVE', '计算机科学专业学生'),
('student2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '李同学', 'STUDENT', 'ACTIVE', '软件工程专业学生');

-- 插入课程数据
INSERT INTO courses (title, description, cover_image, instructor, category, level, duration, price, original_price, rating, student_count, lesson_count, status, tags, teacher_id) VALUES
('Vue.js 3.0 完整开发教程', '从零开始学习Vue.js 3.0，包含组合式API、响应式系统、路由管理等核心概念', 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400', '李老师', '前端开发', 'INTERMEDIATE', 1200, 199.00, 299.00, 4.8, 1234, 24, 'PUBLISHED', 'Vue.js,前端,JavaScript,组件化', 2),
('React 现代开发实践', '学习React最新特性，包含Hooks、Context、性能优化等', 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400', '李老师', '前端开发', 'INTERMEDIATE', 1500, 249.00, 349.00, 4.7, 967, 30, 'PUBLISHED', 'React,Hooks,前端,JavaScript', 2),
('Spring Boot 微服务实战', '深入学习Spring Boot框架，构建企业级微服务应用', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400', '王老师', '后端开发', 'ADVANCED', 1800, 299.00, 399.00, 4.9, 856, 36, 'PUBLISHED', 'Spring Boot,微服务,Java,后端', 3),
('Python 数据分析入门', '使用Python进行数据分析，包含pandas、numpy、matplotlib等库的使用', 'https://images.unsplash.com/photo-1526379879527-8559ecfcaec0?w=400', '王老师', '数据科学', 'BEGINNER', 900, 149.00, 199.00, 4.6, 1456, 18, 'PUBLISHED', 'Python,数据分析,pandas,numpy', 3),
('Node.js 全栈开发', '使用Node.js构建全栈应用，包含Express、MongoDB等技术栈', 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400', '李老师', '全栈开发', 'INTERMEDIATE', 1350, 229.00, 299.00, 4.5, 743, 27, 'PUBLISHED', 'Node.js,Express,MongoDB,全栈', 2);

-- 插入实验数据
INSERT INTO experiments (title, description, cover_image, subject, difficulty, duration, rating, completed_count, objectives, equipment, steps, tags, status) VALUES
('单摆运动实验', '通过改变摆长和初始角度，观察单摆的运动规律，验证单摆周期公式', 'https://images.unsplash.com/photo-1636466497217-26a8cbeaf0aa?w=400', '物理', 'BEGINNER', 30, 4.7, 1234, '理解单摆运动规律,验证周期公式,掌握实验操作方法', '摆球,细线,支架,量角器,秒表', '设置摆长,调整初始角度,释放摆球,测量周期,记录数据,分析结果', '物理,力学,周期运动', 'ACTIVE'),
('酸碱中和滴定实验', '使用标准NaOH溶液滴定未知浓度的HCl溶液，学习滴定操作和终点判断', 'https://images.unsplash.com/photo-1532187863486-abf9dbad1b69?w=400', '化学', 'INTERMEDIATE', 45, 4.8, 987, '掌握滴定操作技能,学习终点判断方法,计算未知溶液浓度', '滴定管,锥形瓶,NaOH标准溶液,HCl待测溶液,酚酞指示剂', '准备溶液,装填滴定管,加入指示剂,开始滴定,观察颜色变化,记录消耗体积', '化学,分析化学,滴定', 'ACTIVE'),
('光的折射实验', '使用激光笔和半圆形玻璃砖，验证光的折射定律', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400', '物理', 'BEGINNER', 25, 4.6, 756, '验证折射定律,测量折射率,理解光的传播规律', '激光笔,半圆形玻璃砖,量角器,白纸,铅笔', '设置实验装置,调整入射角,观察折射光线,测量角度,记录数据,计算折射率', '物理,光学,折射', 'ACTIVE'),
('电解水实验', '通过电解水实验，验证水的分子组成，观察氢气和氧气的产生', 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400', '化学', 'BEGINNER', 35, 4.5, 643, '验证水的分子式,观察电解现象,收集和检验气体', '电解器,直流电源,导线,试管,氢氧化钠溶液', '连接电路,加入电解质,通电电解,收集气体,检验气体,分析结果', '化学,电化学,分解反应', 'ACTIVE');

-- 插入帖子数据
INSERT INTO posts (title, content, category, tags, view_count, like_count, comment_count, is_pinned, status, author_id) VALUES
('Vue 3.0 学习心得分享', '最近在学习Vue 3.0，感觉组合式API真的很强大，特别是在逻辑复用方面。想和大家分享一些学习心得...', '学习交流', 'Vue.js,前端,学习心得', 1234, 89, 23, TRUE, 'PUBLISHED', 4),
('Spring Boot 项目实战经验', '在做毕业设计的过程中，使用Spring Boot构建了一个完整的后端系统，遇到了一些问题，也积累了一些经验...', '技术讨论', 'Spring Boot,后端,项目实战', 987, 67, 18, FALSE, 'PUBLISHED', 5),
('前端面试题总结', '最近在准备前端面试，整理了一些常见的面试题，包括JavaScript、CSS、框架相关的问题...', '求职就业', '面试,前端,JavaScript', 2156, 145, 42, TRUE, 'PUBLISHED', 4),
('如何高效学习编程？', '作为一个从零开始学编程的学生，想分享一些我认为比较有效的学习方法和经验...', '学习交流', '学习方法,编程,经验分享', 1567, 98, 31, FALSE, 'PUBLISHED', 5),
('React Hooks 最佳实践', '使用React Hooks已经有一段时间了，总结了一些最佳实践和常见的坑，希望对大家有帮助...', '技术讨论', 'React,Hooks,最佳实践', 876, 54, 16, FALSE, 'PUBLISHED', 4);

-- 插入学习小组数据
INSERT INTO study_groups (name, description, cover_image, category, max_members, current_members, is_public, tags, status, creator_id) VALUES
('前端技术交流群', '专注于前端技术讨论和学习，欢迎Vue、React、Angular爱好者加入', 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=200', '前端开发', 100, 45, TRUE, '前端,Vue,React,技术交流', 'ACTIVE', 4),
('Java后端开发小组', 'Java后端技术学习小组，分享Spring Boot、微服务等技术经验', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=200', '后端开发', 80, 32, TRUE, 'Java,Spring Boot,后端,微服务', 'ACTIVE', 5),
('算法与数据结构', '一起刷题，一起进步！专注算法和数据结构的学习讨论', 'https://images.unsplash.com/photo-1509966756634-9c23dd6e6815?w=200', '算法', 150, 78, TRUE, '算法,数据结构,刷题,面试', 'ACTIVE', 4),
('Python数据分析小组', '学习Python在数据分析领域的应用，包括pandas、numpy等库', 'https://images.unsplash.com/photo-1526379879527-8559ecfcaec0?w=200', '数据科学', 60, 29, TRUE, 'Python,数据分析,pandas,numpy', 'ACTIVE', 5),
('全栈开发者联盟', '全栈开发技术交流，涵盖前端、后端、数据库等各个方面', 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=200', '全栈开发', 120, 56, TRUE, '全栈,前端,后端,数据库', 'ACTIVE', 4);

-- 插入新闻数据
INSERT INTO news (title, summary, content, cover_image, category, tags, source, author, view_count, like_count, is_hot, status, published_at) VALUES
('Vue 3.4 正式发布，带来重大性能提升', 'Vue.js 团队发布了 3.4 版本，在编译器优化、响应式系统等方面带来显著改进', 'Vue.js 3.4 版本正式发布，这个版本在多个方面带来了重大改进...', 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400', '前端技术', 'Vue.js,前端,更新', 'Vue官方', 'Vue团队', 2345, 156, TRUE, 'PUBLISHED', '2024-01-15 10:00:00'),
('Spring Boot 3.2 发布，支持虚拟线程', 'Spring Boot 3.2 版本发布，正式支持 Java 21 的虚拟线程特性，大幅提升并发性能', 'Spring Boot 3.2 版本带来了对 Java 21 虚拟线程的完整支持...', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400', '后端技术', 'Spring Boot,Java,虚拟线程', 'Spring官方', 'Spring团队', 1876, 98, TRUE, 'PUBLISHED', '2024-01-10 14:30:00'),
('2024年前端开发趋势预测', '分析2024年前端开发的主要趋势，包括框架发展、工具链演进等', '2024年前端开发将继续快速发展，本文分析了几个主要趋势...', 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400', '行业动态', '前端,趋势,2024', 'TechNews', '技术编辑', 1234, 67, FALSE, 'PUBLISHED', '2024-01-08 09:15:00'),
('人工智能在教育领域的应用前景', '探讨AI技术如何改变传统教育模式，提升学习效率和个性化体验', '人工智能技术正在深刻改变各个行业，教育领域也不例外...', 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400', '教育科技', 'AI,教育,技术', 'EduTech', '教育专家', 1654, 89, FALSE, 'PUBLISHED', '2024-01-05 16:20:00'),
('MySQL 8.0 新特性详解', 'MySQL 8.0 版本带来了众多新特性，包括窗口函数、CTE、JSON增强等', 'MySQL 8.0 是一个里程碑式的版本，引入了许多重要的新特性...', 'https://images.unsplash.com/photo-1544383835-bda2bc66a55d?w=400', '数据库技术', 'MySQL,数据库,新特性', 'MySQL官方', 'MySQL团队', 987, 45, FALSE, 'PUBLISHED', '2024-01-03 11:45:00');

-- 插入学习记录数据
INSERT INTO learning_records (user_id, course_id, progress, last_position, completed_at) VALUES
(4, 1, 75, 18, NULL),  -- 张同学学习Vue课程，进度75%
(4, 2, 30, 9, NULL),   -- 张同学学习React课程，进度30%
(4, 5, 100, 27, '2024-01-20 15:30:00'),  -- 张同学完成Node.js课程
(5, 3, 90, 32, NULL),  -- 李同学学习Spring Boot课程，进度90%
(5, 1, 45, 11, NULL),  -- 李同学学习Vue课程，进度45%
(5, 4, 100, 18, '2024-01-18 14:20:00'),  -- 李同学完成Python课程
(2, 1, 100, 24, '2024-01-15 10:00:00'),  -- 李老师完成Vue课程（作为学习者）
(3, 2, 60, 18, NULL);  -- 王老师学习React课程，进度60%

-- ========================================
-- 数据库脚本执行完成
-- ========================================

-- 查看数据统计
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'courses', COUNT(*) FROM courses
UNION ALL
SELECT 'experiments', COUNT(*) FROM experiments
UNION ALL
SELECT 'posts', COUNT(*) FROM posts
UNION ALL
SELECT 'study_groups', COUNT(*) FROM study_groups
UNION ALL
SELECT 'news', COUNT(*) FROM news
UNION ALL
SELECT 'learning_records', COUNT(*) FROM learning_records;
