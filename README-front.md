# DIV教育学习平台

一个基于Vue 3的现代化在线教育学习平台，提供丰富的学习资源、活跃的社区交流和个性化的学习体验。

## 🌟 项目特色

- **现代化技术栈**: Vue 3 + Vite + Element Plus + Pinia
- **响应式设计**: 完美适配PC端和移动端
- **丰富的功能**: 课程学习、社区交流、学习小组、个人中心
- **优秀的用户体验**: 流畅的动画效果和直观的界面设计
- **完整的Mock数据**: 支持完整的增删改查操作演示

## 🚀 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🔐 用户注册

请通过注册页面创建您的账号，或联系管理员获取访问权限。

## 🎯 主要功能

### 🏠 首页
- 精美的轮播图展示
- 课程分类导航
- 热门课程推荐
- 最新简讯预览
- 平台统计数据

### 📚 学习中心
- 课程列表浏览
- 多维度搜索筛选（分类、难度、关键词）
- 课程详情展示
- 学习进度跟踪
- 分页功能

### 💬 交流社区
- 帖子发布和浏览
- 点赞和评论功能
- 用户互动
- 内容搜索

### 📰 简讯
- 技术资讯展示
- 分类浏览
- 热门标记

### 👥 学习小组
- 小组创建和管理
- 成员管理
- 小组讨论

### 👤 个人中心
- 用户信息管理
- 学习记录查看
- 个人设置
- 密码修改

## 🛠️ 技术架构

### 前端技术栈
- **Vue 3**: 渐进式JavaScript框架
- **Vite**: 现代化的前端构建工具
- **Element Plus**: Vue 3的UI组件库
- **Pinia**: Vue的状态管理库
- **Vue Router**: Vue的官方路由管理器

### 项目结构
```
src/
├── components/          # 公共组件
│   ├── Layout/         # 布局组件
│   └── Course/         # 课程相关组件
├── views/              # 页面组件
├── stores/             # Pinia状态管理
├── router/             # 路由配置
├── mock/               # Mock数据和API
└── main.ts             # 应用入口
```

## 📊 Mock数据

项目包含完整的Mock数据服务，模拟真实的后端API：

- **用户管理**: 登录、注册、个人信息
- **课程管理**: 课程CRUD、分类管理、学习记录
- **社区管理**: 帖子CRUD、评论管理、点赞功能
- **简讯管理**: 资讯展示、分类筛选
- **学习小组**: 小组管理、成员管理

## 🎨 设计特色

- **现代化UI**: 采用Element Plus设计语言
- **响应式布局**: 适配各种屏幕尺寸
- **流畅动画**: 丰富的过渡效果和交互动画
- **用户友好**: 直观的操作流程和清晰的信息架构

## 📈 项目状态

- ✅ 前端基础架构完成
- ✅ 核心页面组件完成
- ✅ Mock数据服务完成
- ✅ 用户认证系统完成
- ✅ 响应式设计完成
- ⏳ 后端API开发（可选）
- ⏳ 数据库集成（可选）

---

**注意**: 本项目是为答辩演示而开发的教育学习平台原型，展示了现代Web开发的最佳实践和完整的前端解决方案。
