@echo off
echo ========================================
echo DIV教育学习平台后端服务启动脚本
echo ========================================

REM 创建临时目录
if not exist "tmp" mkdir tmp

echo.
echo 请选择数据库类型:
echo 1. H2内存数据库 (默认，开发环境)
echo 2. MySQL数据库 (生产环境)
echo.
set /p choice=请输入选择 (1 或 2，默认为1):

if "%choice%"=="2" (
    echo 使用MySQL数据库启动...
    set DB_TYPE=mysql
) else (
    echo 使用H2内存数据库启动...
    set DB_TYPE=h2
)

echo.
echo 启动参数: database.type=%DB_TYPE%
echo.

REM 设置JVM参数并启动应用
set JAVA_OPTS=-Djava.io.tmpdir=./tmp -Ddatabase.type=%DB_TYPE%
mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

pause
