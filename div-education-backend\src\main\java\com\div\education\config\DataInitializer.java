package com.div.education.config;

import com.div.education.entity.*;
import com.div.education.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 数据初始化器 - 根据数据库类型自动初始化测试数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Order(2) // 在DatabaseInfoService之后执行
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final CourseRepository courseRepository;
    private final ExperimentRepository experimentRepository;
    private final PostRepository postRepository;
    private final PasswordEncoder passwordEncoder;

    @Value("${database.type:h2}")
    private String databaseType;

    @Override
    public void run(String... args) throws Exception {
        log.info("数据初始化器启动...");
        log.info("当前数据库类型: {}", databaseType.toUpperCase());

        // 检查是否已有数据
        if (userRepository.count() > 0) {
            log.info("数据库中已存在数据，跳过自动初始化");
            log.info("提示：如需重新初始化，请先执行完整的MySQL脚本");
            printCurrentDataCount();
            return;
        }

        log.info("数据库为空，开始自动初始化测试数据...");

        // 初始化用户数据
        initUsers();

        // 初始化课程数据
        initCourses();

        // 初始化实验数据
        initExperiments();

        // 初始化帖子数据
        initPosts();

        log.info("测试数据初始化完成！");
        log.info("用户数量: {}", userRepository.count());
        log.info("课程数量: {}", courseRepository.count());
        log.info("实验数量: {}", experimentRepository.count());
        log.info("帖子数量: {}", postRepository.count());

        // 显示测试账号信息
        printTestAccounts();
    }

    private void printTestAccounts() {
        log.info("========================================");
        log.info("测试账号信息:");
        log.info("管理员: <EMAIL> / 123456");
        log.info("教师1: <EMAIL> / 123456");
        log.info("教师2: <EMAIL> / 123456");
        log.info("学生1: <EMAIL> / 123456");
        log.info("学生2: <EMAIL> / 123456");
        log.info("========================================");
    }

    private void initUsers() {
        // 管理员
        User admin = new User();
        admin.setUsername("admin");
        admin.setEmail("<EMAIL>");
        admin.setPassword(passwordEncoder.encode("123456"));
        admin.setNickname("系统管理员");
        admin.setRole(User.UserRole.ADMIN);
        admin.setStatus(User.UserStatus.ACTIVE);
        admin.setBio("系统管理员账号");
        userRepository.save(admin);

        // 教师
        User teacher1 = new User();
        teacher1.setUsername("teacher1");
        teacher1.setEmail("<EMAIL>");
        teacher1.setPassword(passwordEncoder.encode("123456"));
        teacher1.setNickname("李老师");
        teacher1.setRole(User.UserRole.TEACHER);
        teacher1.setStatus(User.UserStatus.ACTIVE);
        teacher1.setBio("前端开发讲师，拥有10年开发经验");
        userRepository.save(teacher1);

        User teacher2 = new User();
        teacher2.setUsername("teacher2");
        teacher2.setEmail("<EMAIL>");
        teacher2.setPassword(passwordEncoder.encode("123456"));
        teacher2.setNickname("王老师");
        teacher2.setRole(User.UserRole.TEACHER);
        teacher2.setStatus(User.UserStatus.ACTIVE);
        teacher2.setBio("后端开发专家，Java技术栈资深讲师");
        userRepository.save(teacher2);

        // 学生
        User student1 = new User();
        student1.setUsername("student1");
        student1.setEmail("<EMAIL>");
        student1.setPassword(passwordEncoder.encode("123456"));
        student1.setNickname("张同学");
        student1.setRole(User.UserRole.STUDENT);
        student1.setStatus(User.UserStatus.ACTIVE);
        student1.setBio("计算机科学专业学生");
        userRepository.save(student1);

        User student2 = new User();
        student2.setUsername("student2");
        student2.setEmail("<EMAIL>");
        student2.setPassword(passwordEncoder.encode("123456"));
        student2.setNickname("李同学");
        student2.setRole(User.UserRole.STUDENT);
        student2.setStatus(User.UserStatus.ACTIVE);
        student2.setBio("软件工程专业学生");
        userRepository.save(student2);
    }

    private void initCourses() {
        User teacher1 = userRepository.findByUsername("teacher1").orElse(null);
        User teacher2 = userRepository.findByUsername("teacher2").orElse(null);

        if (teacher1 != null) {
            Course course1 = new Course();
            course1.setTitle("Vue.js 3.0 完整开发教程");
            course1.setDescription("从零开始学习Vue.js 3.0，包含组合式API、响应式系统、路由管理等核心概念");
            course1.setCoverImage("https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400");
            course1.setInstructor("李老师");
            course1.setCategory("前端开发");
            course1.setLevel("INTERMEDIATE");
            course1.setDuration(1200);
            course1.setPrice(new BigDecimal("199.00"));
            course1.setOriginalPrice(new BigDecimal("299.00"));
            course1.setRating(new BigDecimal("4.8"));
            course1.setStudentCount(1234);
            course1.setLessonCount(24);
            course1.setStatus(Course.CourseStatus.PUBLISHED);
            course1.setTags("Vue.js,前端,JavaScript,组件化");
            course1.setTeacher(teacher1);
            courseRepository.save(course1);

            Course course2 = new Course();
            course2.setTitle("React 现代开发实践");
            course2.setDescription("学习React最新特性，包含Hooks、Context、性能优化等");
            course2.setCoverImage("https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400");
            course2.setInstructor("李老师");
            course2.setCategory("前端开发");
            course2.setLevel("INTERMEDIATE");
            course2.setDuration(1500);
            course2.setPrice(new BigDecimal("249.00"));
            course2.setOriginalPrice(new BigDecimal("349.00"));
            course2.setRating(new BigDecimal("4.7"));
            course2.setStudentCount(967);
            course2.setLessonCount(30);
            course2.setStatus(Course.CourseStatus.PUBLISHED);
            course2.setTags("React,Hooks,前端,JavaScript");
            course2.setTeacher(teacher1);
            courseRepository.save(course2);
        }

        if (teacher2 != null) {
            Course course3 = new Course();
            course3.setTitle("Spring Boot 微服务实战");
            course3.setDescription("深入学习Spring Boot框架，构建企业级微服务应用");
            course3.setCoverImage("https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400");
            course3.setInstructor("王老师");
            course3.setCategory("后端开发");
            course3.setLevel("ADVANCED");
            course3.setDuration(1800);
            course3.setPrice(new BigDecimal("299.00"));
            course3.setOriginalPrice(new BigDecimal("399.00"));
            course3.setRating(new BigDecimal("4.9"));
            course3.setStudentCount(856);
            course3.setLessonCount(36);
            course3.setStatus(Course.CourseStatus.PUBLISHED);
            course3.setTags("Spring Boot,微服务,Java,后端");
            course3.setTeacher(teacher2);
            courseRepository.save(course3);
        }
    }

    private void initExperiments() {
        Experiment exp1 = new Experiment();
        exp1.setTitle("单摆运动实验");
        exp1.setDescription("通过改变摆长和初始角度，观察单摆的运动规律，验证单摆周期公式");
        exp1.setCoverImage("https://images.unsplash.com/photo-1636466497217-26a8cbeaf0aa?w=400");
        exp1.setSubject("物理");
        exp1.setDifficulty(Experiment.ExperimentDifficulty.BEGINNER);
        exp1.setDuration(30);
        exp1.setRating(new BigDecimal("4.7"));
        exp1.setCompletedCount(1234);
        exp1.setObjectives("理解单摆运动规律,验证周期公式,掌握实验操作方法");
        exp1.setEquipment("摆球,细线,支架,量角器,秒表");
        exp1.setSteps("设置摆长,调整初始角度,释放摆球,测量周期,记录数据,分析结果");
        exp1.setTags("物理,力学,周期运动");
        exp1.setStatus(Experiment.ExperimentStatus.ACTIVE);
        experimentRepository.save(exp1);

        Experiment exp2 = new Experiment();
        exp2.setTitle("酸碱中和滴定实验");
        exp2.setDescription("使用标准NaOH溶液滴定未知浓度的HCl溶液，学习滴定操作和终点判断");
        exp2.setCoverImage("https://images.unsplash.com/photo-1532187863486-abf9dbad1b69?w=400");
        exp2.setSubject("化学");
        exp2.setDifficulty(Experiment.ExperimentDifficulty.INTERMEDIATE);
        exp2.setDuration(45);
        exp2.setRating(new BigDecimal("4.8"));
        exp2.setCompletedCount(987);
        exp2.setObjectives("掌握滴定操作技能,学习终点判断方法,计算未知溶液浓度");
        exp2.setEquipment("滴定管,锥形瓶,NaOH标准溶液,HCl待测溶液,酚酞指示剂");
        exp2.setSteps("准备溶液,装填滴定管,加入指示剂,开始滴定,观察颜色变化,记录消耗体积");
        exp2.setTags("化学,分析化学,滴定");
        exp2.setStatus(Experiment.ExperimentStatus.ACTIVE);
        experimentRepository.save(exp2);
    }

    private void initPosts() {
        User student1 = userRepository.findByUsername("student1").orElse(null);
        User student2 = userRepository.findByUsername("student2").orElse(null);

        if (student1 != null) {
            Post post1 = new Post();
            post1.setTitle("Vue 3.0 学习心得分享");
            post1.setContent("最近在学习Vue 3.0，感觉组合式API真的很强大，特别是在逻辑复用方面。想和大家分享一些学习心得...");
            post1.setCategory("学习交流");
            post1.setTags("Vue.js,前端,学习心得");
            post1.setViewCount(1234);
            post1.setLikeCount(89);
            post1.setCommentCount(23);
            post1.setIsPinned(true);
            post1.setStatus(Post.PostStatus.PUBLISHED);
            post1.setAuthor(student1);
            postRepository.save(post1);
        }

        if (student2 != null) {
            Post post2 = new Post();
            post2.setTitle("Spring Boot 项目实战经验");
            post2.setContent("在做毕业设计的过程中，使用Spring Boot构建了一个完整的后端系统，遇到了一些问题，也积累了一些经验...");
            post2.setCategory("技术讨论");
            post2.setTags("Spring Boot,后端,项目实战");
            post2.setViewCount(987);
            post2.setLikeCount(67);
            post2.setCommentCount(18);
            post2.setIsPinned(false);
            post2.setStatus(Post.PostStatus.PUBLISHED);
            post2.setAuthor(student2);
            postRepository.save(post2);
        }
    }

    private void printCurrentDataCount() {
        log.info("========================================");
        log.info("当前数据库统计:");
        log.info("用户数量: {}", userRepository.count());
        log.info("课程数量: {}", courseRepository.count());
        log.info("实验数量: {}", experimentRepository.count());
        log.info("帖子数量: {}", postRepository.count());
        log.info("========================================");
    }
}
