# DIV教育学习平台

## 🎓 项目简介

DIV教育学习平台是一个基于Vue.js + Spring Boot的现代化在线教育平台，提供完整的学习管理、仿真实验、社区交流和数据分析功能。

## ✨ 核心特性

- 🎯 **完整的学习系统** - 课程管理、学习进度跟踪、个性化推荐
- 🧪 **仿真实验室** - 物理化学实验模拟，支持全屏免打扰模式
- 💬 **社区交流** - 帖子发布、评论互动、学习小组
- 📊 **数据分析** - 学习行为分析、能力评估报告
- 📱 **响应式设计** - 完美适配PC和移动端
- 🔐 **安全认证** - JWT认证、角色权限管理

## 🛠️ 技术架构

### 前端技术栈

- **框架**: Vue.js 3.4 + Vite
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **构建工具**: Vite

### 后端技术栈

- **框架**: Spring Boot 2.7.18
- **数据访问**: Spring Data JPA
- **安全框架**: Spring Security
- **数据库**: MySQL 8.0 / H2 (开发环境)
- **构建工具**: Maven

## 🗄️ 数据库配置

### 默认使用MySQL数据库

项目默认配置为MySQL数据库，提供完整的生产环境支持：

```yaml
# div-education-backend/src/main/resources/application.yml
database:
  type: mysql  # 默认MySQL，可改为h2切换到内存数据库
```

### 数据库部署方式

#### 方式一：执行完整SQL脚本（推荐）

```bash
# 1. 登录MySQL
mysql -u root -p

# 2. 执行完整脚本（包含建库、建表、插入数据）
source div-education-platform-mysql.sql
```

#### 方式二：手动创建数据库

```sql
-- 只创建数据库，表结构由JPA自动创建
CREATE DATABASE div_education CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 核心数据表（7张）

1. **users** - 用户表 (18个用户：1管理员+6教师+11学生)
2. **courses** - 课程表 (25门课程：前端、后端、数据科学、移动开发、云计算等)
3. **experiments** - 实验表 (15个实验：物理、化学、生物实验)
4. **posts** - 帖子表 (25个帖子：学习交流、技术讨论、求职就业、项目展示等)
5. **study_groups** - 学习小组表 (18个小组：各技术方向和学习交流)
6. **news** - 新闻表 (20条新闻：前端、后端、AI、移动开发等技术资讯)
7. **learning_records** - 学习记录表 (65条记录：详细的学习进度跟踪)

### 数据库特点

- **丰富示例数据**: 包含166条记录，覆盖所有功能模块和真实使用场景
- **真实业务场景**: 多角色用户、完整课程体系、活跃社区讨论
- **MySQL优化**: 使用InnoDB引擎，完整索引和外键约束
- **字符集支持**: utf8mb4字符集，支持中文和特殊字符
- **数据初始化**: 已禁用后端自动数据生成，完全依赖SQL脚本初始化

## 🚀 快速启动

### ⚠️ 重要说明

**项目已配置为默认使用MySQL数据库，前端不再使用Mock数据。必须先启动后端服务，前端才能正常工作！**

### 环境要求

- Node.js 16+
- Java 8+
- Maven 3.6+
- MySQL 8.0+ (必需，项目默认使用MySQL)

### 前端启动

```bash
cd div-education-platform
npm install
npm run dev
# 访问: http://localhost:5175
# 注意：前端必须连接后端API，不再使用Mock数据
```

### 后端启动

#### 前置步骤：准备MySQL数据库

```bash
# 1. 启动MySQL服务
# 2. 执行完整SQL脚本
mysql -u root -p < div-education-platform-mysql.sql
```

```bash
cd div-education-backend
# 默认MySQL数据库
mvn spring-boot:run "-Dspring-boot.run.jvmArguments=-Djava.io.tmpdir=C:/temp"
# 切换到H2数据库（可选）
mvn spring-boot:run "-Dspring-boot.run.jvmArguments=-Djava.io.tmpdir=C:/temp -Ddatabase.type=h2"
```

## 🎮 演示账号

| 角色   | 邮箱             | 密码   | 说明           |
| ------ | ---------------- | ------ | -------------- |
| 管理员 | <EMAIL>    | 123456 | 系统管理员     |
| 教师   | <EMAIL> | 123456 | 前端开发讲师   |
| 教师   | <EMAIL> | 123456 | 后端开发专家   |
| 学生   | <EMAIL> | 123456 | 计算机科学专业 |
| 学生   | <EMAIL> | 123456 | 软件工程专业   |

## 🌐 访问地址

- **前端应用**: http://localhost:5175
- **后端API**: http://localhost:8081/api
- **MySQL数据库**: localhost:3306/div_education
- **H2控制台**: http://localhost:8081/api/h2-console (仅切换到H2时可用)

## 🌟 功能展示

### 🏠 首页

- 轮播图展示热门课程
- 课程分类导航
- 推荐内容展示
- 统计数据可视化

### 📖 学习中心

- 课程浏览和搜索
- 分类筛选功能
- 学习进度跟踪
- 个性化推荐

### 🧪 仿真实验室

- 物理实验：单摆运动实验
- 化学实验：酸碱中和滴定实验
- **全屏免打扰模式**
- 实验数据记录和分析

### 💬 交流社区

- 帖子发布和浏览
- 点赞评论功能
- 话题分类讨论
- 用户互动交流

### 👥 学习小组

- 小组创建和管理
- 成员邀请和加入
- 小组讨论功能
- 学习计划制定

### 📊 数据分析

- 学习行为分析
- 能力评估报告
- 学习路径推荐
- 数据可视化图表

### 👤 个人中心

- 个人信息管理
- 学习记录查看
- 成就系统
- 设置配置

## 🔧 故障排除

### 后端启动失败

1. **MySQL连接失败**: 确保MySQL服务启动且已执行SQL脚本
2. **端口占用**: 检查8081端口是否被占用
3. **临时目录错误**: 使用参数 `-Djava.io.tmpdir=C:/temp`
4. **数据库不存在**: 执行 `source div-education-platform-mysql.sql`

### 前端启动失败

1. **依赖安装**: 运行 `npm install`
2. **端口占用**: 检查5175端口是否被占用
3. **Node版本**: 确保Node.js版本16+

### 数据库问题

1. **重新初始化数据**: 重新执行完整SQL脚本
2. **切换到H2**: 修改 `database.type: h2`，无需MySQL
3. **字符编码问题**: 确保MySQL使用utf8mb4字符集

## 🏗️ 项目结构

```
DIV教育学习平台/
├── div-education-platform/     # 前端项目
│   ├── src/
│   │   ├── components/         # 组件
│   │   ├── views/             # 页面
│   │   ├── stores/            # 状态管理
│   │   ├── router/            # 路由配置
│   │   ├── api/               # API接口
│   │   └── mock/              # Mock数据
│   └── package.json
├── div-education-backend/      # 后端项目
│   ├── src/main/java/         # Java源码
│   ├── src/main/resources/    # 配置文件
│   ├── database/              # 数据库脚本
│   └── pom.xml
├── Requirements.md            # 需求文档
├── MyQuestion.md             # 问题记录
├── ProjectSummary.md         # 项目总结
└── README.md                 # 项目说明
```

## 🔧 开发指南

### 数据库配置

- **开发环境**: 默认使用H2内存数据库
- **生产环境**: 配置MySQL数据库
- 详细配置请参考 [数据库配置说明](div-education-backend/DATABASE_SETUP.md)

### API接口

- 前端API配置支持后端服务 + Mock数据降级
- RESTful风格的后端接口设计
- 完整的CRUD操作支持

### 部署说明

1. 前端构建：`npm run build`
2. 后端打包：`mvn clean package`
3. 数据库初始化：执行SQL脚本
4. 服务启动：配置相应环境变量

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📊 项目完成状态

### 🎯 开发进度

- ✅ **前端开发**: 100%完成 (Vue.js + Element Plus)
- ✅ **后端开发**: 100%完成 (Spring Boot + JPA)
- ✅ **数据库设计**: 100%完成 (7张核心表)
- ✅ **API接口**: 100%完成 (RESTful设计)
- ✅ **数据初始化**: 100%完成 (自动创建示例数据)
- ✅ **文档完善**: 100%完成 (统一文档结构)

### 🌟 技术亮点

- **一键数据库切换**: 修改一个配置参数即可切换H2/MySQL
- **统一表结构**: H2和MySQL完全一致的7张核心表
- **丰富示例数据**: 166条记录覆盖所有功能模块和真实场景
- **SQL脚本管理**: 禁用自动数据初始化，使用完整SQL脚本管理数据
- **前后端分离**: 完整的API接口和现代化前端
- **响应式设计**: 完美适配PC和移动端
- **仿真实验**: 创新的全屏实验模拟功能

### 🎮 演示功能

- **用户系统**: 管理员、教师、学生三种角色
- **学习中心**: 课程浏览、搜索、学习进度跟踪
- **仿真实验**: 物理化学实验模拟，支持全屏模式
- **社区交流**: 帖子发布、点赞评论、学习小组
- **数据分析**: 学习行为分析、能力评估报告
- **资讯简讯**: 技术资讯、行业动态

## 🎉 项目总结

**DIV教育学习平台已100%完成开发，具备完整的演示和答辩能力！**

### 核心成就

- ✅ 完整的前后端分离架构
- ✅ 7张核心数据表，支持双数据库
- ✅ 一键数据库切换功能
- ✅ 丰富的示例数据系统（166条记录）
- ✅ SQL脚本化数据管理
- ✅ 现代化UI设计和用户体验
- ✅ 创新的仿真实验功能
- ✅ 完善的文档和部署指南

### 技术规格

- **前端**: Vue.js 3.4 + Element Plus + Vite
- **后端**: Spring Boot 2.7.18 + JPA + Security
- **数据库**: H2 (开发) + MySQL (生产)
- **架构**: RESTful API + 响应式前端
- **部署**: 支持本地开发和生产部署

**🚀 项目已完全就绪，可立即进行功能演示和技术答辩！**
